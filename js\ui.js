import { player } from './player.js';
import { makeChoice } from './main.js';

const storyContainer = document.getElementById('story-container');
const hudElements = {
    location: document.getElementById('hud-location'),
    time: document.getElementById('hud-time'),
    cp: document.getElementById('hud-cp'),
    sp: document.getElementById('hud-sp'),
    dxp: document.getElementById('hud-dxp'),
};
const modalContainer = document.getElementById('modal-container');

let currentModals = {};

export function updateHUD() {
    hudElements.location.textContent = player.location;
    hudElements.time.textContent = player.time;
    hudElements.cp.textContent = player.stats.cp;
    hudElements.sp.textContent = player.stats.sp;
    hudElements.dxp.textContent = player.stats.dxp;
}

export function renderScene(sceneId, sceneData) {
    if (!sceneData) {
        console.error(`Scene data not found for sceneId: ${sceneId}`);
        return;
    }

    if (sceneData.onLoad) {
        sceneData.onLoad();
    }

    let html = `<div id="${sceneId}">${sceneData.text}</div>`;

    if (sceneData.choices && sceneData.choices.length > 0) {
        html += `<div class="choice-section">`;
        sceneData.choices.forEach((choice, index) => {
            // We pass the entire choice object to makeChoice now
            html += `<a href="#" class="choice-button" data-choice-index="${index}">${choice.label}</a>`;
        });
        html += `</div>`;
    }
    
    storyContainer.innerHTML = html;
    addChoiceListeners(sceneData.choices);
    updateHUD();
    window.scrollTo(0, 0);
}

function addChoiceListeners(choices) {
    if (!choices) return;
    const buttons = document.querySelectorAll('.choice-button');
    buttons.forEach(button => {
        button.addEventListener('click', (event) => {
            event.preventDefault();
            const choiceIndex = event.currentTarget.getAttribute('data-choice-index');
            makeChoice(choices[choiceIndex]);
        });
    });
}

export function showModal(modalId) {
    const modalData = currentModals[modalId];
    if (!modalData) return;

    let modalHTML = `
        <div id="${modalId}Modal" class="modal" style="display:block;">
            <div class="modal-content">
                <span class="close-button" onclick="window.ui.closeModal('${modalId}')">&times;</span>
                <div class="modal-header">${modalData.title}</div>
                ${modalData.content}
            </div>
        </div>
    `;
    modalContainer.innerHTML = modalHTML;
}

export function closeModal(modalId) {
    const modal = document.getElementById(modalId + 'Modal');
    if (modal) {
        modal.style.display = 'none';
    }
    modalContainer.innerHTML = '';
}

export function setCurrentModals(modals) {
    currentModals = modals;
}