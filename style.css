body {
    background-color: #121212;
    color: #e0e0e0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 1.1em;
    line-height: 1.7;
    margin: 0;
    padding: 40px 20px 120px 20px;
}

.container {
    max-width: 800px;
    margin: 0 auto;
}

/* --- HUD --- */
.hud {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.85);
    border-top: 2px solid #b38f00;
    padding: 10px 20px;
    box-sizing: border-box;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.9em;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    z-index: 100;
}
.hud-item {
    margin: 5px 15px;
    color: #d4af37;
}
.hud-item strong {
    color: #a0a0a0;
}

/* --- Story & Prompts --- */
.system-prompt {
    background-color: rgba(17, 34, 68, 0.5);
    border: 1px solid #3366cc;
    border-radius: 5px;
    padding: 15px;
    margin: 25px 0;
}
.prompt-title {
    color: #66aaff;
    font-weight: bold;
    margin-bottom: 10px;
}
.reward {
    color: #2eff7b;
}
.penalty {
    color: #ff4d4d;
}

/* --- Choices --- */
.choice-section {
    margin-top: 30px;
    padding: 20px;
    background-color: #1f1f1f;
    border-left: 4px solid #b38f00;
}
.choice-button {
    display: block;
    width: 90%;
    margin: 15px auto;
    padding: 15px;
    background-color: #b38f00;
    color: #000;
    text-align: center;
    text-decoration: none;
    font-weight: bold;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.1s;
}
.choice-button:hover {
    background-color: #d4af37;
}
.choice-button:active {
    transform: scale(0.98);
}

/* --- Special Text --- */
em {
    color: #b0b0b0;
    font-style: italic;
}
strong {
    color: #ff6b6b;
}
.character-name {
    color: #d4af37;
    cursor: pointer;
    font-weight: bold;
}
.character-name:hover {
    text-decoration: underline;
}

/* --- Modals --- */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.8);
    animation: fadeIn 0.3s;
}
.modal-content {
    background-color: #1a1a1a;
    margin: 10% auto;
    padding: 25px;
    border: 1px solid #b38f00;
    width: 90%;
    max-width: 600px;
    border-radius: 10px;
    font-family: 'Courier New', Courier, monospace;
}
.modal-header {
    color: #b38f00;
    font-size: 1.3em;
    margin-bottom: 20px;
    border-bottom: 1px solid #444;
    padding-bottom: 10px;
}
.close-button {
    color: #aaa;
    float: right;
    font-size: 32px;
    font-weight: bold;
    line-height: 0.8;
}
.close-button:hover,
.close-button:focus {
    color: white;
    text-decoration: none;
    cursor: pointer;
}

@keyframes fadeIn {
    from {opacity: 0;}
    to {opacity: 1;}
}